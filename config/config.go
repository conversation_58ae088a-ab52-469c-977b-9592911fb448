package config

import (
	"fmt"
	"strings"

	"github.com/spf13/viper"
)

// LogConfig 定义日志配置
type LogConfig struct {
	Level string `mapstructure:"level"`
}

// WebSocketConfig 定义 WebSocket 服务器配置
type WebSocketConfig struct {
	Enabled         bool   `mapstructure:"enabled"`
	Host            string `mapstructure:"host"`
	Port            string `mapstructure:"port"`
	ReadBufferSize  int    `mapstructure:"read_buffer_size"`
	WriteBufferSize int    `mapstructure:"write_buffer_size"`
	CheckOrigin     bool   `mapstructure:"check_origin"`
}

// DatabaseConfig 数据库选择配置
type DatabaseConfig struct {
	Type string `mapstructure:"type"` // "tdengine" 或 "influxdb"
}

// Config 代表应用程序全局配置
type Config struct {
	Database  DatabaseConfig  `mapstructure:"database"`
	Kafka     KafkaConfig     `mapstructure:"kafka"`
	TDengine  TDengineConfig  `mapstructure:"tdengine"`
	InfluxDB  InfluxDBConfig  `mapstructure:"influxdb"`
	Log       LogConfig       `mapstructure:"log"`
	WebSocket WebSocketConfig `mapstructure:"websocket"`
}

// KafkaConfig holds Kafka related configuration.
type KafkaConfig struct {
	Brokers          []string `mapstructure:"brokers"`
	InputTopic       string   `mapstructure:"input_topic"`
	OutputTopic      string   `mapstructure:"output_topic"`
	AddressesTopic   string   `mapstructure:"addresses_topic"`
	TokensTopic      string   `mapstructure:"tokens_topic"`
	TokenOutputTopic string   `mapstructure:"token_output_topic"`
	GroupID          string   `mapstructure:"group_id"` // Kafka consumer group ID
	NumConsumers     int      `mapstructure:"num_consumers"`
	UseAWSIAM        bool     `mapstructure:"use_aws_iam"` // Whether to use AWS IAM auth for MSK
	AWSRegion        string   `mapstructure:"aws_region"`  // AWS Region for MSK
	// 性能优化配置
	MinBytes          int  `mapstructure:"min_bytes"`           // 最小批次大小
	MaxBytes          int  `mapstructure:"max_bytes"`           // 最大批次大小
	CommitInterval    int  `mapstructure:"commit_interval_ms"`  // 提交间隔(毫秒)
	StartFromEarliest bool `mapstructure:"start_from_earliest"` // 是否从最早的消息开始消费
}

// TDengineConfig holds TDengine related configuration.
type TDengineConfig struct {
	Host     string `mapstructure:"host"`
	Port     string `mapstructure:"port"`
	User     string `mapstructure:"user"`
	Password string `mapstructure:"password"`
	Database string `mapstructure:"database"`
}

// InfluxDBConfig holds InfluxDB related configuration.
type InfluxDBConfig struct {
	Host         string `mapstructure:"host"`
	Port         string `mapstructure:"port"`
	Token        string `mapstructure:"token"`
	Organization string `mapstructure:"organization"`
	Bucket       string `mapstructure:"bucket"`
	Username     string `mapstructure:"username"`
	Password     string `mapstructure:"password"`
}

// Cfg holds the global configuration instance.
var Cfg *Config

// LoadConfig reads configuration from file and environment variables.
func LoadConfig(configPath string) (*Config, error) {
	v := viper.New()

	// --- Set Defaults ---
	// Database defaults
	v.SetDefault("database.type", "influxdb")

	// Kafka defaults
	v.SetDefault("kafka.brokers", []string{"127.0.0.1:9092"})
	v.SetDefault("kafka.input_topic", "origin-tx")
	v.SetDefault("kafka.output_topic", "final-tx")
	v.SetDefault("kafka.addresses_topic", "addresses")
	v.SetDefault("kafka.tokens_topic", "tokens")
	v.SetDefault("kafka.token_output_topic", "token-output")
	v.SetDefault("kafka.group_id", "model-processor-group-1")
	v.SetDefault("kafka.num_consumers", 1)
	v.SetDefault("kafka.use_aws_iam", false)
	v.SetDefault("kafka.aws_region", "eu-central-1")
	v.SetDefault("kafka.min_bytes", 1000)
	v.SetDefault("kafka.max_bytes", 1048576)
	v.SetDefault("kafka.commit_interval_ms", 1000)
	v.SetDefault("kafka.start_from_earliest", true)

	// TDengine defaults
	v.SetDefault("tdengine.host", "localhost")
	v.SetDefault("tdengine.port", "6041")
	v.SetDefault("tdengine.user", "root")
	v.SetDefault("tdengine.password", "taosdata")
	v.SetDefault("tdengine.database", "") // No default for required database

	// InfluxDB defaults
	v.SetDefault("influxdb.host", "localhost")
	v.SetDefault("influxdb.port", "8086")
	v.SetDefault("influxdb.token", "")
	v.SetDefault("influxdb.organization", "forgex")
	v.SetDefault("influxdb.bucket", "forgex-test") // Default bucket nam
	v.SetDefault("influxdb.username", "")
	v.SetDefault("influxdb.password", "")

	// Log defaults
	v.SetDefault("log.level", "info")

	// --- Read Config File ---
	if configPath != "" {
		v.SetConfigFile(configPath)
	} else {
		v.SetConfigName("config")
		v.SetConfigType("yaml")
		v.AddConfigPath(".")
	}

	if err := v.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			fmt.Println("Config file not found, using defaults/environment variables.")
		} else {
			return nil, fmt.Errorf("error reading config file: %w", err)
		}
	} else {
		fmt.Printf("Using config file: %s\n", v.ConfigFileUsed())
	}

	// --- Environment Variable Overrides ---
	v.SetEnvPrefix("")
	v.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))
	v.AutomaticEnv()

	// --- Unmarshal Config into the global Cfg variable ---
	if err := v.Unmarshal(&Cfg); err != nil { // Unmarshal directly into the global Cfg
		return nil, fmt.Errorf("error unmarshaling config: %w", err)
	}

	// --- Validation ---
	if Cfg == nil {
		return nil, fmt.Errorf("config unmarshaling resulted in nil Cfg")
	}

	// 验证数据库类型
	if Cfg.Database.Type != "tdengine" && Cfg.Database.Type != "influxdb" {
		return nil, fmt.Errorf("invalid database type: %s, must be 'tdengine' or 'influxdb'", Cfg.Database.Type)
	}

	// 根据数据库类型验证相应的配置
	switch Cfg.Database.Type {
	case "tdengine":
		if Cfg.TDengine.Database == "" {
			return nil, fmt.Errorf("tdengine.database is required when using TDengine")
		}
	case "influxdb":
		if Cfg.InfluxDB.Organization == "" {
			return nil, fmt.Errorf("influxdb.organization is required when using InfluxDB")
		}
		if Cfg.InfluxDB.Bucket == "" {
			return nil, fmt.Errorf("influxdb.bucket is required when using InfluxDB")
		}
	}

	// Return the populated global Cfg instance
	return Cfg, nil
}
