package kafka

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"model-processor/config"

	"github.com/segmentio/kafka-go"
)

// Client holds Kafka connection details and clients.
type Client struct {
	writer   *kafka.Writer
	readers  []*kafka.Reader
	topics   map[string]string // map of topic name to topic type (input/output/addresses)
	brokers  []string
	groupID  string
	counters struct {
		sync.Mutex
		inputMessages   int64
		addressMessages int64
	}
	numConsumers int // 输入主题消费者数量
}

// MessageHandler defines the callback function type for processing consumed messages.
type MessageHandler func(value []byte, partitionID int32) error

// AddressHandler defines the callback function for processing address messages.
type AddressHandler func(key, value []byte) error

// NewClient creates and initializes a new Kafka client using the provided config.
func NewClient(cfg config.KafkaConfig) (*Client, error) {
	brokers := cfg.Brokers
	if len(brokers) == 0 || brokers[0] == "" {
		return nil, fmt.Errorf("kafka brokers are not configured")
	}

	groupID := cfg.GroupID
	if groupID == "" {
		groupID = "model-processor-group-1"
	}

	topics := make(map[string]string)
	if cfg.InputTopic != "" {
		topics[cfg.InputTopic] = "input"
	}
	if cfg.OutputTopic != "" {
		topics[cfg.OutputTopic] = "output"
	}
	if cfg.AddressesTopic != "" {
		topics[cfg.AddressesTopic] = "addresses"
	}
	if cfg.TokensTopic != "" {
		topics[cfg.TokensTopic] = "tokens"
	}
	if cfg.TokenOutputTopic != "" {
		topics[cfg.TokenOutputTopic] = "token_output"
	}

	numConsumers := 3
	if cfg.NumConsumers > 0 {
		numConsumers = cfg.NumConsumers
	}

	writer := &kafka.Writer{
		Addr:     kafka.TCP(brokers...),
		Topic:    cfg.OutputTopic,
		Balancer: &kafka.Hash{},
	}

	log.Printf("Kafka writer created successfully for brokers: %v", brokers)
	log.Printf("Using consumer group ID: %s with %d workers", groupID, numConsumers)

	return &Client{
		writer:       writer,
		topics:       topics,
		brokers:      brokers,
		groupID:      groupID,
		numConsumers: numConsumers,
	}, nil
}

// Close cleans up the Kafka writer and readers.
func (c *Client) Close() {
	log.Println("Closing Kafka client...")
	if c.writer != nil {
		c.writer.Close()
	}
	// Readers will be closed by their respective goroutines via defer
	log.Println("Kafka client closed.")
}

// GetMessageCounts returns the current message counts for each topic type
func (c *Client) GetMessageCounts() (inputCount, addressCount int64) {
	c.counters.Lock()
	defer c.counters.Unlock()
	return c.counters.inputMessages, c.counters.addressMessages
}

// ProduceMessage sends a message to the configured output topic.
func (c *Client) ProduceMessage(key, value []byte) error {
	msg := kafka.Message{
		Key:   key,
		Value: value,
	}
	return c.writer.WriteMessages(context.Background(), msg)
}

// ProduceMessageToTopic sends a message to the specified topic type
func (c *Client) ProduceMessageToTopic(topicType string, key, value []byte) error {
	var topicName string
	found := false

	if topicType == "" {
		topicType = "output"
	}

	for topic, tType := range c.topics {
		if tType == topicType {
			topicName = topic
			found = true
			break
		}
	}

	if !found || topicName == "" {
		return fmt.Errorf("no appropriate topic found for type %s", topicType)
	}

	w := &kafka.Writer{
		Addr:     kafka.TCP(c.brokers...),
		Topic:    topicName,
		Balancer: &kafka.Hash{},
	}
	defer w.Close()

	msg := kafka.Message{
		Key:   key,
		Value: value,
	}
	return w.WriteMessages(context.Background(), msg)
}

// 优化：提取readerConfig构建和消费循环为内部函数，减少重复，增加日志和recover
func (c *Client) StartConsumer(ctx context.Context, wg *sync.WaitGroup, cfg config.KafkaConfig, txHandler func([]byte, int32) error, addrHandler func([]byte, []byte) error, tokenHandler func([]byte, []byte) error) error {
	var inputTopic, addressesTopic, tokensTopic string
	for topic, topicType := range c.topics {
		if topicType == "input" {
			inputTopic = topic
		} else if topicType == "addresses" {
			addressesTopic = topic
		} else if topicType == "tokens" {
			tokensTopic = topic
		}
	}

	buildReaderConfig := func(topic, groupID string) kafka.ReaderConfig {
		startOffset := kafka.LastOffset
		if cfg.StartFromEarliest {
			startOffset = kafka.FirstOffset
		}
		commitInterval := time.Duration(0)
		if cfg.CommitInterval > 0 {
			commitInterval = time.Duration(cfg.CommitInterval) * time.Millisecond
		}
		readerConfig := kafka.ReaderConfig{
			Brokers:     c.brokers,
			Topic:       topic,
			GroupID:     groupID,
			MinBytes:    cfg.MinBytes,
			MaxBytes:    cfg.MaxBytes,
			StartOffset: startOffset,
		}
		if commitInterval > 0 {
			readerConfig.CommitInterval = commitInterval
		}
		return readerConfig
	}

	consumeLoop := func(r *kafka.Reader, topicType, topicName string, handler func(m kafka.Message), logPrefix string) {
		defer wg.Done()
		defer r.Close()
		log.Printf("%s: Consumer started for topic %s", logPrefix, topicName)
		for {
			if ctx.Err() != nil {
				log.Printf("%s: Context cancelled, stopping consumer for topic %s", logPrefix, topicName)
				return
			}
			readCtx, cancel := context.WithTimeout(ctx, 5*time.Second)
			m, err := r.ReadMessage(readCtx)
			cancel()
			if err != nil {
				if ctx.Err() != nil {
					log.Printf("%s: Context cancelled, stopping consumer for topic %s", logPrefix, topicName)
					return
				}
				if readCtx.Err() == context.DeadlineExceeded {
					continue
				}
				log.Printf("%s: Error reading message from topic %s: %v", logPrefix, topicName, err)
				continue
			}
			// handler保护，防止panic
			func() {
				defer func() {
					if rec := recover(); rec != nil {
						log.Printf("%s: Handler panic recovered: %v", logPrefix, rec)
					}
				}()
				handler(m)
			}()
		}
	}

	if inputTopic != "" {
		for i := 0; i < c.numConsumers; i++ {
			reader := kafka.NewReader(buildReaderConfig(inputTopic, c.groupID))
			c.readers = append(c.readers, reader)
			wg.Add(1)
			go consumeLoop(reader, "input", inputTopic, func(m kafka.Message) {
				c.counters.Lock()
				c.counters.inputMessages++
				c.counters.Unlock()
				if txHandler != nil {
					txHandler(m.Value, int32(m.Partition))
				}
			},
				fmt.Sprintf("Worker-%d", i+1),
			)
		}
	}

	if addressesTopic != "" && addrHandler != nil {
		reader := kafka.NewReader(buildReaderConfig(addressesTopic, c.groupID+"-addresses"))
		c.readers = append(c.readers, reader)
		wg.Add(1)
		go consumeLoop(reader, "addresses", addressesTopic, func(m kafka.Message) {
			c.counters.Lock()
			c.counters.addressMessages++
			c.counters.Unlock()
			addrHandler(m.Key, m.Value)
		}, "Addresses")
	}

	if tokensTopic != "" && tokenHandler != nil {
		reader := kafka.NewReader(buildReaderConfig(tokensTopic, c.groupID+"-tokens"))
		c.readers = append(c.readers, reader)
		wg.Add(1)
		go consumeLoop(reader, "tokens", tokensTopic, func(m kafka.Message) {
			tokenHandler(m.Key, m.Value)
		}, "Tokens")
	}

	return nil
}
