package model

import (
	"time"
)

// TimeSeriesDB 定义时序数据库的通用接口
// 所有时序数据库实现都应该实现这个接口
type TimeSeriesDB interface {
	// 初始化数据库连接
	Init(config interface{}) error

	// 关闭数据库连接
	Close() error

	// 插入单条记录
	InsertMemeTx(tx MemeTx, network string, tokenAddr string) error

	// 使用线程专用连接插入记录（性能优化）
	InsertMemeTxWithThreadID(tx MemeTx, network string, tokenAddr string, threadID int) error

	// 批量插入记录
	InsertMemeTxBatch(tx MemeTx, network string, tokenAddr string, threadID int) error

	// 根据交易哈希查询记录
	QueryMemeTxByHash(txHash string) ([]MemeTx, error)

	// 根据用户地址查询记录
	QueryMemeTxByUser(userAddr string, limit int) ([]MemeTx, error)

	// 根据代币地址查询记录
	QueryMemeTxByToken(tokenAddr string, limit int) ([]MemeTx, error)

	// 根据时间范围查询记录
	QueryMemeTxByTimeRange(startTime, endTime time.Time, limit int) ([]MemeTx, error)

	// 删除记录
	DeleteMemeTxByHash(txHash string) (int64, error)

	// 获取数据库健康状态
	HealthCheck() error

	// 获取数据库类型
	GetType() string
}

// DatabaseType 数据库类型枚举
type DatabaseType string

const (
	DatabaseTypeTDengine DatabaseType = "tdengine"
	DatabaseTypeInfluxDB DatabaseType = "influxdb"
)

// 注意：实际的数据库初始化和管理逻辑已移至 database_manager.go
// 这里保留接口定义以供引用
