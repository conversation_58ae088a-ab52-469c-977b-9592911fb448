package model

import (
	"fmt"
	"testing"
	"time"

	"model-processor/config"
)

// TestInfluxDBSimple 测试InfluxDB数据库操作
func TestInfluxDBSimple(t *testing.T) {
	// 配置测试数据库连接
	testConfig := config.InfluxDBConfig{
		Host:         "127.0.0.1",
		Port:         "8086",
		Token:        "test-token-12345678901234567890",
		Organization: "test-org",
		Bucket:       "test-bucket",
		Username:     "admin",
		Password:     "password123",
	}

	// 创建数据库实例
	db := &InfluxDBSimple{}

	// 测试初始化
	t.Run("Init", func(t *testing.T) {
		err := db.Init(testConfig)
		if err != nil {
			t.Skipf("跳过InfluxDB测试，数据库连接失败: %v", err)
		}
	})

	// 确保在测试结束后关闭连接
	defer func() {
		if db.client != nil {
			db.Close()
		}
	}()

	// 如果初始化失败，跳过后续测试
	if db.client == nil {
		t.<PERSON><PERSON>("InfluxDB数据库未连接，跳过所有测试")
	}

	// 测试数据库类型
	t.Run("GetType", func(t *testing.T) {
		dbType := db.GetType()
		if dbType != "influxdb" {
			t.Errorf("期望数据库类型为 'influxdb'，实际得到 '%s'", dbType)
		}
	})

	// 测试健康检查
	t.Run("HealthCheck", func(t *testing.T) {
		err := db.HealthCheck()
		if err != nil {
			t.Errorf("健康检查失败: %v", err)
		}
	})

	// 创建测试交易数据
	testTx := createTestInfluxMemeTx(t)
	testNetwork := "solana"
	testTokenAddr := fmt.Sprintf("test_token_%d", time.Now().UnixNano())

	// 测试单条插入
	t.Run("InsertMemeTx", func(t *testing.T) {
		err := db.InsertMemeTx(testTx, testNetwork, testTokenAddr)
		if err != nil {
			t.Errorf("单条插入失败: %v", err)
		}
	})

	// 测试线程专用插入
	t.Run("InsertMemeTxWithThreadID", func(t *testing.T) {
		testTx.TxHash = fmt.Sprintf("thread_test_%d", time.Now().UnixNano())
		err := db.InsertMemeTxWithThreadID(testTx, testNetwork, testTokenAddr, 1)
		if err != nil {
			t.Errorf("线程专用插入失败: %v", err)
		}
	})

	// 测试批量插入
	t.Run("InsertMemeTxBatch", func(t *testing.T) {
		batchSize := 150 // 超过缓冲区大小以触发刷新

		for i := 0; i < batchSize; i++ {
			tx := testTx
			tx.TxHash = fmt.Sprintf("batch_test_%d_%d", time.Now().UnixNano(), i)
			tx.TxIndex = int64(i)

			err := db.InsertMemeTxBatch(tx, testNetwork, testTokenAddr, 2)
			if err != nil {
				t.Errorf("批量插入第%d条失败: %v", i, err)
			}
		}

		// 等待批量刷新完成
		time.Sleep(3 * time.Second)
	})

	// 测试Point创建功能

	t.Run("CreateTxPoint", func(t *testing.T) {
		point := db.createTxPoint(testTx, testNetwork, testTokenAddr)
		if point == nil {
			t.Error("代币交易Point创建失败")
		} else {
			t.Logf("代币交易Point创建成功: %s", point.Name())
		}
	})

	// 测试查询功能（现在已实现）
	t.Run("QueryMethods", func(t *testing.T) {
		// 等待数据写入完成
		time.Sleep(2 * time.Second)

		// 测试按哈希查询
		results, err := db.QueryMemeTxByHash(testTx.TxHash)
		if err != nil {
			t.Logf("按哈希查询失败: %v", err)
		} else {
			t.Logf("按哈希查询返回 %d 条结果", len(results))
		}

		// 测试按用户查询
		results, err = db.QueryMemeTxByUser(testTx.UserAddr, 10)
		if err != nil {
			t.Logf("按用户查询失败: %v", err)
		} else {
			t.Logf("按用户查询返回 %d 条结果", len(results))
		}

		// 测试按代币查询
		results, err = db.QueryMemeTxByToken(testTokenAddr, 10)
		if err != nil {
			t.Logf("按代币查询失败: %v", err)
		} else {
			t.Logf("按代币查询返回 %d 条结果", len(results))
		}

		// 测试按时间范围查询
		startTime := time.Now().Add(-1 * time.Hour)
		endTime := time.Now().Add(1 * time.Hour)
		results, err = db.QueryMemeTxByTimeRange(startTime, endTime, 10)
		if err != nil {
			t.Logf("按时间范围查询失败: %v", err)
		} else {
			t.Logf("按时间范围查询返回 %d 条结果", len(results))
		}

		// 测试删除功能
		deleted, err := db.DeleteMemeTxByHash(testTx.TxHash)
		if err != nil {
			t.Logf("删除失败: %v", err)
		} else {
			t.Logf("成功删除 %d 条记录", deleted)
		}
	})
}

// TestInfluxDBBatchPerformance 测试InfluxDB批量操作性能
func TestInfluxDBBatchPerformance(t *testing.T) {
	testConfig := config.InfluxDBConfig{
		Host:         "127.0.0.1",
		Port:         "8086",
		Token:        "test-token-12345678901234567890",
		Organization: "test-org",
		Bucket:       "test-bucket",
		Username:     "admin",
		Password:     "password123",
	}

	db := &InfluxDBSimple{}
	err := db.Init(testConfig)
	if err != nil {
		t.Skipf("跳过性能测试，数据库连接失败: %v", err)
	}
	defer db.Close()

	// 性能测试：批量插入1000条记录
	t.Run("BatchPerformance1000", func(t *testing.T) {
		batchCount := 1000
		testNetwork := "solana"
		testTokenAddr := fmt.Sprintf("perf_token_%d", time.Now().UnixNano())

		startTime := time.Now()

		for i := 0; i < batchCount; i++ {
			tx := createTestInfluxMemeTx(t)
			tx.TxHash = fmt.Sprintf("perf_test_%d_%d", time.Now().UnixNano(), i)
			tx.TxIndex = int64(i)

			err := db.InsertMemeTxBatch(tx, testNetwork, testTokenAddr, 3)
			if err != nil {
				t.Errorf("性能测试插入第%d条失败: %v", i, err)
			}
		}

		// 等待所有批量操作完成
		time.Sleep(5 * time.Second)

		duration := time.Since(startTime)
		rate := float64(batchCount) / duration.Seconds()

		t.Logf("InfluxDB批量插入性能测试:")
		t.Logf("- 插入记录数: %d", batchCount)
		t.Logf("- 总耗时: %v", duration)
		t.Logf("- 插入速率: %.2f 记录/秒", rate)

		if rate < 90 {
			t.Errorf("批量插入性能过低: %.2f 记录/秒 < 90 记录/秒", rate)
		}
	})
}

// TestInfluxDBErrorHandling 测试InfluxDB错误处理
func TestInfluxDBErrorHandling(t *testing.T) {
	// 创建未初始化的数据库实例进行错误测试
	db := &InfluxDBSimple{}

	// 测试未初始化时的健康检查
	t.Run("HealthCheckWithoutInit", func(t *testing.T) {
		err := db.HealthCheck()
		if err == nil {
			t.Error("期望未初始化的健康检查返回错误")
		}
	})

	// 测试错误的配置类型
	t.Run("InitWithWrongConfig", func(t *testing.T) {
		err := db.Init("wrong config type")
		if err == nil {
			t.Error("期望错误的配置类型返回错误")
		}
	})

	// 测试无效的配置
	t.Run("InitWithInvalidConfig", func(t *testing.T) {
		invalidConfig := config.InfluxDBConfig{
			Host: "",
			Port: "",
		}
		err := db.Init(invalidConfig)
		// 这个可能不会立即失败，因为客户端创建时不一定验证连接
		t.Logf("使用无效配置初始化: %v", err)
	})
}

// TestInfluxDBDataValidation 测试InfluxDB数据验证
func TestInfluxDBDataValidation(t *testing.T) {
	testConfig := config.InfluxDBConfig{
		Host:         "127.0.0.1",
		Port:         "8086",
		Token:        "test-token-12345678901234567890",
		Organization: "test-org",
		Bucket:       "test-bucket",
		Username:     "admin",
		Password:     "password123",
	}

	db := &InfluxDBSimple{}
	err := db.Init(testConfig)
	if err != nil {
		t.Skipf("跳过数据验证测试，数据库连接失败: %v", err)
	}
	defer db.Close()

	// 测试特殊字符处理
	t.Run("SpecialCharacters", func(t *testing.T) {
		testTx := createTestInfluxMemeTx(t)
		testTx.TxHash = "test_hash_with_special_chars_!@#$%^&*()"
		testTx.UserAddr = "user_with_quotes_'_and_\"_chars"
		testTx.AmountIn = "123.456789"
		testTx.AmountOut = "987.654321"

		err := db.InsertMemeTx(testTx, "solana", "special_token")
		if err != nil {
			t.Errorf("特殊字符处理失败: %v", err)
		}
	})

	// 测试空值处理
	t.Run("EmptyValues", func(t *testing.T) {
		testTx := createTestInfluxMemeTx(t)
		testTx.UserAddr = ""
		testTx.AmountIn = ""
		testTx.AmountOut = ""

		err := db.InsertMemeTx(testTx, "solana", "")
		if err != nil {
			t.Errorf("空值处理失败: %v", err)
		}
	})

	// 测试大数值处理
	t.Run("LargeNumbers", func(t *testing.T) {
		testTx := createTestInfluxMemeTx(t)
		testTx.AmountIn = "999999999999999999999.999999999"
		testTx.AmountOut = "0.000000000000000001"

		err := db.InsertMemeTx(testTx, "solana", "large_number_token")
		if err != nil {
			t.Errorf("大数值处理失败: %v", err)
		}
	})
}

// createTestInfluxMemeTx 创建测试用的MemeTx数据
func createTestInfluxMemeTx(t *testing.T) MemeTx {
	return MemeTx{
		Ts:               time.Now(),
		BlockTime:        time.Now().Unix(),
		TxHash:           fmt.Sprintf("influx_test_hash_%d", time.Now().UnixNano()),
		UserAddr:         fmt.Sprintf("influx_test_user_%d", time.Now().UnixNano()),
		TokenInAddr:      "So11111111111111111111111111111111111111112", // WSOL
		TokenOutAddr:     fmt.Sprintf("influx_token_out_%d", time.Now().UnixNano()),
		AmountIn:         "2000.75",
		AmountOut:        "1999.25",
		TxType:           "SELL",
		BlockSlot:        int64(time.Now().Unix()),
		InstructionIndex: 2,
		TxIndex:          2,
	}
}
