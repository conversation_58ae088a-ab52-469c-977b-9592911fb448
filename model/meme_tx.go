package model

import (
	"time"
)

type MemeTx struct {
	Ts               time.Time `json:"ts"`
	BlockTime        int64     `json:"block_time"`     // 从time.Time改为int64 (BIGINT)
	TxHash           string    `json:"tx_hash"`        // VARCHAR(128)
	UserAddr         string    `json:"user_addr"`      // VARCHAR(128)
	TokenInAddr      string    `json:"token_in_addr"`  // VARCHAR(128)
	TokenOutAddr     string    `json:"token_out_addr"` // VARCHAR(128)
	AmountIn         string    `json:"amount_in"`      // VARCHAR(128)
	AmountOut        string    `json:"amount_out"`     // VARCHAR(128)
	TxType           string    `json:"tx_type"`        // NCHAR(16)
	BlockSlot        int64     `json:"block_slot"`     // BIGINT
	InstructionIndex int32     `json:"instruction_index"`
	TxIndex          int64     `json:"tx_index"` // INT
	// Network and TokenAddr are tags, handled separately during insertion.
}

// MemeTxWithMeta 包含交易和元数据的结构体
type MemeTxWithMeta struct {
	Tx        MemeTx
	Network   string
	TokenAddr string
	ThreadID  int
}
