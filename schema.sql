CREATE DATABASE forgex KEEP 7d DURATION 1d PRECISION 'us';
-- Create stable for user operations
CREATE STABLE IF NOT EXISTS solana_transaction_user (
  ts            TIMESTAMP,
  block_time    BIGINT,
  tx_hash       VARCHAR(128),
  token_in_addr VARCHAR(128),
  token_out_addr VARCHAR(128),
  amount_in     VARCHAR(128),
  amount_out    VARCHAR(128),
  tx_type       NCHAR(16),
  block_slot    BIGINT,
  instruction_index INT,
  tx_index      BIGINT
) TAGS (
  user_addr     VARCHAR(128)
);

-- Create stable for token operations
  CREATE STABLE IF NOT EXISTS solana_transaction_token (
    ts            TIMESTAMP,
    block_time    BIGINT,
    tx_hash       VARCHAR(128),
    user_addr     VARCHAR(128),
    token_in_addr VARCHAR(128),
    token_out_addr VARCHAR(128),
    amount_in     VARCHAR(128),
    amount_out    VARCHAR(128),
    tx_type       NCHAR(16),
    block_slot    BIGINT,
    instruction_index INT,
    tx_index      BIGINT
  ) TAGS (
    token_addr    VARCHAR(128)
  );

-- Create stable for storing all processed transaction hashes
CREATE STABLE IF NOT EXISTS processed_txhash (
  ts            TIMESTAMP,
  tx_hash       VARCHAR(128),
  block_time    BIGINT,
  network       VARCHAR(16),
  status        VARCHAR(16),  -- 'processed', 'failed', etc.
  process_time  TIMESTAMP
) TAGS (
  block_slot    BIGINT    -- block_slot 作为标签
);
