package service

import (
	"encoding/json"
	"log"
	"sync"
)

// AddressUpdate represents the structure of messages from the addresses topic
type AddressUpdate struct {
	Wallets []string `json:"wallets"`
}

// AddressService handles the business logic for processing address updates
// 现在只按 groupId 维护，不再区分 userId
type AddressService struct {
	// 主索引 - 钱包地址 -> set<groupId>
	walletIndex map[string]map[string]struct{}

	// 反向索引 - groupId -> 地址 -> 存在标志
	groupWallets map[string]map[string]bool // groupId -> 地址 -> 存在标志

	mutex sync.RWMutex // 用于线程安全的访问
}

// NewAddressService creates a new service instance
func NewAddressService() *AddressService {
	return &AddressService{
		walletIndex:  make(map[string]map[string]struct{}),
		groupWallets: make(map[string]map[string]bool),
	}
}

// ProcessUpdate processes a message from the addresses topic based on key and value
func (s *AddressService) ProcessUpdate(messageKey []byte, messageValue []byte) error {
	log.Printf("[AddressService] ProcessUpdate called: key=%s, value=%s", string(messageKey), string(messageValue))
	// key 直接就是 groupId
	groupId := string(messageKey)
	if groupId == "" {
		log.Printf("Invalid message key: empty groupId")
		return nil
	}
	// 解析钱包列表
	var wallets []string
	if err := json.Unmarshal(messageValue, &wallets); err != nil {
		if string(messageValue) == "" || string(messageValue) == "null" || string(messageValue) == "[]" {
			wallets = []string{}
			log.Printf("Received empty or null message value for key %s, proceeding with clearing logic.", groupId)
		} else {
			log.Printf("Failed to unmarshal address array message for key %s: %v. Value: %s", groupId, err, string(messageValue))
			return err
		}
	}

	// 空钱包列表 -> 清空该 groupId 的所有地址
	if len(wallets) == 0 {
		log.Printf("Received clear command for groupId: %s (Empty wallet list)", groupId)
		s.clearGroupAddresses(groupId)
		return nil
	}

	// 非空钱包列表 -> 替换该 groupId 的所有地址
	log.Printf("Received update command for groupId: %s, Wallet count: %d", groupId, len(wallets))
	s.updateWallets(groupId, wallets)
	return nil
}

// clearGroupAddresses removes all wallets for a specific groupId.
func (s *AddressService) clearGroupAddresses(groupId string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if wallets, exists := s.groupWallets[groupId]; exists {
		for wallet := range wallets {
			if s.walletIndex[wallet] != nil {
				delete(s.walletIndex[wallet], groupId)
				if len(s.walletIndex[wallet]) == 0 {
					delete(s.walletIndex, wallet)
				}
			}
		}
		delete(s.groupWallets, groupId)
	}
}

// updateWallets updates (replaces) the wallet list for a specific groupId.
func (s *AddressService) updateWallets(groupId string, newWallets []string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// 1. Remove old wallets for this groupId from the main index
	if oldWallets, exists := s.groupWallets[groupId]; exists {
		for wallet := range oldWallets {
			if s.walletIndex[wallet] != nil {
				delete(s.walletIndex[wallet], groupId)
				if len(s.walletIndex[wallet]) == 0 {
					delete(s.walletIndex, wallet)
				}
			}
		}
	}

	// 2. Create the new set of wallets for this groupId
	currentWalletSet := make(map[string]bool)
	for _, wallet := range newWallets {
		currentWalletSet[wallet] = true
		if s.walletIndex[wallet] == nil {
			s.walletIndex[wallet] = make(map[string]struct{})
		}
		s.walletIndex[wallet][groupId] = struct{}{}
	}

	// 3. Update the group mapping
	s.groupWallets[groupId] = currentWalletSet
	log.Printf("Updated addresses for groupId: %s. Total wallets now: %d", groupId, len(s.walletIndex))
}

// isWalletUsedByOtherGroups checks if a wallet exists in any group other than the excludeKey.
// This function MUST be called within a locked context.
func (s *AddressService) isWalletUsedByOtherGroups(wallet, excludeKey string) bool {
	for key, wallets := range s.groupWallets {
		if key == excludeKey {
			continue
		}
		if _, exists := wallets[wallet]; exists {
			return true // Found in another group
		}
	}
	return false // Not found in any other group
}

// IsWalletStored checks if a wallet address is in the main index.
func (s *AddressService) IsWalletStored(wallet string) bool {
	s.mutex.RLock()
	defer s.mutex.RUnlock()
	set, exists := s.walletIndex[wallet]
	return exists && len(set) > 0
}

// GetStoredWallets returns all stored wallet addresses.
func (s *AddressService) GetStoredWallets() []string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	result := make([]string, 0, len(s.walletIndex))
	for wallet := range s.walletIndex {
		result = append(result, wallet)
	}

	return result
}

// GetWalletsByGroup returns all wallet addresses for a specific groupId.
func (s *AddressService) GetWalletsByGroup(groupId string) []string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	result := make([]string, 0)
	if wallets, exists := s.groupWallets[groupId]; exists {
		for wallet := range wallets {
			result = append(result, wallet)
		}
	}
	return result
}
