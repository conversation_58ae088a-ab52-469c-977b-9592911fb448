package service

import (
	"context"
	"log"
	"sort"
	"sync"
	"time"
)

type TimeWindowStats struct {
	startTime          time.Time
	swapTxCount        uint64
	dbSuccessCount     uint64
	parseErrorCount    uint64
	dbErrorCount       uint64
	addressFilterCount uint64
	tokenForwardCount  uint64
	processErrorCount  uint64

	instructionsProcessed     uint64
	instructionsAttempted     uint64
	totalParseTransactionTime time.Duration
	countParseTransaction     uint64
	totalProcessSwapDataTime  time.Duration
	countProcessSwapData      uint64
	kafkaProduceUserSuccess   uint64
	kafkaProduceUserError     uint64
	kafkaForwardError         uint64

	partitionMessageCounts map[int32]uint64
	partitionLock          sync.Mutex

	// 添加最大block_slot和对应的txhash跟踪
	maxBlockSlot       uint64
	maxBlockSlotTxHash string
	blockSlotLock      sync.Mutex
}

func NewTimeWindowStats() *TimeWindowStats {
	return &TimeWindowStats{
		startTime:              time.Now(),
		partitionMessageCounts: make(map[int32]uint64),
	}
}

func (s *TimeWindowStats) Reset() {
	s.startTime = time.Now()
	s.swapTxCount = 0
	s.dbSuccessCount = 0
	s.parseErrorCount = 0
	s.dbErrorCount = 0
	s.addressFilterCount = 0
	s.tokenForwardCount = 0
	s.processErrorCount = 0
	s.instructionsProcessed = 0
	s.instructionsAttempted = 0
	s.totalParseTransactionTime = 0
	s.countParseTransaction = 0
	s.totalProcessSwapDataTime = 0
	s.countProcessSwapData = 0
	s.kafkaProduceUserSuccess = 0
	s.kafkaProduceUserError = 0
	s.kafkaForwardError = 0
	// Reset partition stats
	s.partitionLock.Lock()
	s.partitionMessageCounts = make(map[int32]uint64)
	s.partitionLock.Unlock()
	// Reset block slot tracking
	s.blockSlotLock.Lock()
	s.maxBlockSlot = 0
	s.maxBlockSlotTxHash = ""
	s.blockSlotLock.Unlock()
}

func (s *TimeWindowStats) GetStats() map[string]interface{} {
	duration := time.Since(s.startTime).Seconds()
	if duration < 1 {
		duration = 1
	}
	maxBlockSlot, maxTxHash := s.GetMaxBlockSlotInfo()
	return map[string]interface{}{
		"duration_sec":                 duration,
		"swap_tx_count":                s.swapTxCount,
		"swap_tx_per_sec":              float64(s.swapTxCount) / duration,
		"db_success_count":             s.dbSuccessCount,
		"db_success_per_sec":           float64(s.dbSuccessCount) / duration,
		"parse_error_count":            s.parseErrorCount,
		"db_error_count":               s.dbErrorCount,
		"address_filter_count":         s.addressFilterCount,
		"token_forward_count":          s.tokenForwardCount,
		"process_error_count":          s.processErrorCount,
		"instructions_processed":       s.instructionsProcessed,
		"instructions_attempted":       s.instructionsAttempted,
		"total_parse_transaction_time": s.totalParseTransactionTime,
		"count_parse_transaction":      s.countParseTransaction,
		"total_process_swap_data_time": s.totalProcessSwapDataTime,
		"count_process_swap_data":      s.countProcessSwapData,
		"kafka_produce_user_success":   s.kafkaProduceUserSuccess,
		"kafka_produce_user_error":     s.kafkaProduceUserError,
		"kafka_forward_error":          s.kafkaForwardError,
		"max_block_slot":               maxBlockSlot,
		"max_block_slot_tx_hash":       maxTxHash,
	}
}

type StatsService struct {
	statsLock     sync.Mutex
	currentStats  *TimeWindowStats
	previousStats *TimeWindowStats
}

func NewStatsService() *StatsService {
	return &StatsService{
		currentStats:  NewTimeWindowStats(),
		previousStats: NewTimeWindowStats(),
	}
}

func (s *StatsService) IncrementStat(statType string, value ...interface{}) {
	s.statsLock.Lock()
	defer s.statsLock.Unlock()
	s.incrementWindowStat(statType, value...)
}

func (s *StatsService) incrementWindowStat(statType string, value ...interface{}) {
	switch statType {
	case "swap_tx":
		s.currentStats.swapTxCount++
	case "db_success":
		s.currentStats.dbSuccessCount++
	case "parse_error":
		s.currentStats.parseErrorCount++
	case "db_error":
		s.currentStats.dbErrorCount++
	case "address_filter":
		s.currentStats.addressFilterCount++
	case "token_forward":
		s.currentStats.tokenForwardCount++
	case "process_error":
		s.currentStats.processErrorCount++
	case "instructions_processed":
		s.currentStats.instructionsProcessed++
	case "instructions_attempted":
		s.currentStats.instructionsAttempted++
	case "total_parse_transaction_time":
		if len(value) > 0 {
			if val, ok := value[0].(time.Duration); ok {
				s.currentStats.totalParseTransactionTime += val
				s.currentStats.countParseTransaction++
			}
		}
	case "total_process_swap_data_time":
		if len(value) > 0 {
			if val, ok := value[0].(time.Duration); ok {
				s.currentStats.totalProcessSwapDataTime += val
				s.currentStats.countProcessSwapData++
			}
		}
	case "kafka_produce_user_success":
		s.currentStats.kafkaProduceUserSuccess++
	case "kafka_produce_user_error":
		s.currentStats.kafkaProduceUserError++
	case "kafka_forward_error":
		s.currentStats.kafkaForwardError++
	case "partition_message":
		if len(value) > 0 {
			if partitionID, ok := value[0].(int32); ok {
				s.currentStats.partitionLock.Lock()
				s.currentStats.partitionMessageCounts[partitionID]++
				s.currentStats.partitionLock.Unlock()
			}
		}
	}
}

func (s *StatsService) RotateWindow() {
	s.statsLock.Lock()
	statsToLog := s.currentStats
	s.currentStats = s.previousStats
	s.currentStats.Reset()
	s.previousStats = statsToLog
	s.statsLock.Unlock()
}

func (s *StatsService) StartCollector(ctx context.Context, interval time.Duration, tokenService *TokenService) {
	ticker := time.NewTicker(interval)
	go func() {
		for {
			select {
			case <-ticker.C:
				s.RotateWindow()
				s.PrintStats(tokenService)
			case <-ctx.Done():
				ticker.Stop()
				return
			}
		}
	}()
}

func (s *StatsService) PrintStats(tokenService *TokenService) {
	s.statsLock.Lock()
	statsToLog := s.previousStats
	durationSec := float64(60)
	if !statsToLog.startTime.IsZero() {
		durationSec = time.Since(statsToLog.startTime).Seconds()
	}
	if durationSec < 1 {
		durationSec = 1
	}
	// [流量] 分区统计
	log.Println("[流量] Partition 消息统计:")
	statsToLog.partitionLock.Lock()
	if len(statsToLog.partitionMessageCounts) > 0 {
		var partitions []int32
		for partition := range statsToLog.partitionMessageCounts {
			partitions = append(partitions, partition)
		}
		sort.Slice(partitions, func(i, j int) bool { return partitions[i] < partitions[j] })
		for _, partition := range partitions {
			count := statsToLog.partitionMessageCounts[partition]
			rate := float64(count) / durationSec
			log.Printf("  Partition %d: %d messages (%.2f msg/s)", partition, count, rate)
		}
	} else {
		log.Println("  No partition messages in this window.")
	}
	statsToLog.partitionLock.Unlock()
	// [解析] 解析性能统计
	log.Printf("[解析] InstrAttempt: %d, InstrProc: %d, ParseErr: %d, AvgParseTxT: %.2fms",
		statsToLog.instructionsAttempted,
		statsToLog.instructionsProcessed,
		statsToLog.parseErrorCount,
		func() float64 {
			if statsToLog.countParseTransaction > 0 {
				return (statsToLog.totalParseTransactionTime.Seconds() * 1000) / float64(statsToLog.countParseTransaction)
			}
			return 0
		}(),
	)
	// [业务] 业务处理统计
	log.Printf("[业务] SwapTX: %d (%.2f/s), DB_OK: %d, DB_Err: %d, ProcErr: %d, AddrFilter: %d, TokenFwd: %d",
		statsToLog.swapTxCount, float64(statsToLog.swapTxCount)/durationSec,
		statsToLog.dbSuccessCount,
		statsToLog.dbErrorCount,
		statsToLog.processErrorCount,
		statsToLog.addressFilterCount,
		statsToLog.tokenForwardCount,
	)
	// [Kafka] Kafka 相关统计
	log.Printf("[Kafka] User_OK: %d, User_Err: %d, Fwd_OK: %d, Fwd_Err: %d",
		statsToLog.kafkaProduceUserSuccess,
		statsToLog.kafkaProduceUserError,
		statsToLog.tokenForwardCount,
		statsToLog.kafkaForwardError,
	)
	// [Token] 监控状态
	if tokenService != nil {
		monitoredTokens := tokenService.GetAllMonitoredTokens()
		if len(monitoredTokens) > 0 {
			log.Printf("[Token] 当前监控 %d 个token:", len(monitoredTokens))
			for tokenAddr, targetAddr := range monitoredTokens {
				count := tokenService.GetTokenCount(tokenAddr)
				log.Printf("  - Token: %s, Target: %s, Count: %d", tokenAddr, targetAddr, count)
			}
		} else {
			log.Printf("[Token] 当前无监控token")
		}
	}

	// [Block] 最大block slot信息
	maxBlockSlot, maxTxHash := statsToLog.GetMaxBlockSlotInfo()
	if maxBlockSlot > 0 {
		log.Printf("[Block] 最大BlockSlot: %d, TxHash: %s", maxBlockSlot, maxTxHash)
	} else {
		log.Printf("[Block] 本窗口期内无block slot数据")
	}

	s.statsLock.Unlock()
}

func (s *StatsService) GetDetailedStats() map[string]uint64 {
	s.statsLock.Lock()
	defer s.statsLock.Unlock()
	cs := s.currentStats
	maxBlockSlot, _ := cs.GetMaxBlockSlotInfo()
	return map[string]uint64{
		"swap_tx_count":              cs.swapTxCount,
		"db_success_count":           cs.dbSuccessCount,
		"parse_error_count":          cs.parseErrorCount,
		"process_error_count":        cs.processErrorCount,
		"db_error_count":             cs.dbErrorCount,
		"address_filter_count":       cs.addressFilterCount,
		"token_forward_count":        cs.tokenForwardCount,
		"instructions_attempted":     cs.instructionsAttempted,
		"instructions_processed":     cs.instructionsProcessed,
		"kafka_produce_user_success": cs.kafkaProduceUserSuccess,
		"kafka_produce_user_error":   cs.kafkaProduceUserError,
		"kafka_forward_error":        cs.kafkaForwardError,
		"max_block_slot":             maxBlockSlot,
	}
}

func (s *StatsService) GetPreviousWindowStats() map[string]uint64 {
	s.statsLock.Lock()
	defer s.statsLock.Unlock()
	ps := s.previousStats
	maxBlockSlot, _ := ps.GetMaxBlockSlotInfo()
	return map[string]uint64{
		"swap_tx_count":              ps.swapTxCount,
		"db_success_count":           ps.dbSuccessCount,
		"parse_error_count":          ps.parseErrorCount,
		"process_error_count":        ps.processErrorCount,
		"db_error_count":             ps.dbErrorCount,
		"address_filter_count":       ps.addressFilterCount,
		"token_forward_count":        ps.tokenForwardCount,
		"instructions_attempted":     ps.instructionsAttempted,
		"instructions_processed":     ps.instructionsProcessed,
		"kafka_produce_user_success": ps.kafkaProduceUserSuccess,
		"kafka_produce_user_error":   ps.kafkaProduceUserError,
		"kafka_forward_error":        ps.kafkaForwardError,
		"max_block_slot":             maxBlockSlot,
	}
}

// UpdateMaxBlockSlot 更新最大block slot和对应的txhash
func (s *TimeWindowStats) UpdateMaxBlockSlot(blockSlot uint64, txHash string) {
	s.blockSlotLock.Lock()
	defer s.blockSlotLock.Unlock()

	if blockSlot > s.maxBlockSlot {
		s.maxBlockSlot = blockSlot
		s.maxBlockSlotTxHash = txHash
	}
}

// GetMaxBlockSlotInfo 获取最大block slot信息
func (s *TimeWindowStats) GetMaxBlockSlotInfo() (uint64, string) {
	s.blockSlotLock.Lock()
	defer s.blockSlotLock.Unlock()
	return s.maxBlockSlot, s.maxBlockSlotTxHash
}
