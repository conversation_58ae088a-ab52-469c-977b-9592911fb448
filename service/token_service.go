package service

import (
	"fmt"
	"log"
	"sync"
)

// KafkaTokenProducer 定义了TokenService需要的生产者接口
type KafkaTokenProducer interface {
	ProduceMessage(key, value []byte) error
	ProduceMessageToTopic(topicType string, key, value []byte) error
}

// TokenService 管理需要特别监控的代币地址
type TokenService struct {
	tokenAddresses map[string]string // address -> target topic address
	tokenCounts    map[string]int    // address -> count (引用计数)
	mutex          sync.RWMutex
	producer       KafkaTokenProducer
	targetTopic    string // 目标输出主题，用于发送监控到的交易
}

// NewTokenService 创建一个新的TokenService实例
func NewTokenService(producer KafkaTokenProducer, targetTopic string) *TokenService {
	if producer == nil {
		log.Println("Warning: KafkaTokenProducer is nil in NewTokenService")
	}
	return &TokenService{
		tokenAddresses: make(map[string]string),
		tokenCounts:    make(map[string]int),
		producer:       producer,
		targetTopic:    targetTopic,
	}
}

// ProcessUpdate 处理来自update-token-topic的消息
func (s *TokenService) ProcessUpdate(messageKey, messageValue []byte) error {
	log.Printf("[TokenService] ProcessUpdate called: key=%q, value=%q", messageKey, messageValue)
	if len(messageKey) == 0 {
		log.Println("Error: Empty message key")
		return fmt.Errorf("empty message key")
	}
	tokenAddr := string(messageKey)
	operation := string(messageValue)

	s.mutex.Lock()
	defer s.mutex.Unlock()

	currentCount := s.tokenCounts[tokenAddr]
	_, targetExists := s.tokenAddresses[tokenAddr]

	switch operation {
	case "add", "subscribe":
		s.tokenCounts[tokenAddr] = currentCount + 1
		if !targetExists {
			s.tokenAddresses[tokenAddr] = tokenAddr
			log.Printf("[TokenService] Started monitoring token: %s (count: %d)", tokenAddr, currentCount+1)
		} else {
			log.Printf("[TokenService] Increased monitoring count for token: %s (count: %d)", tokenAddr, currentCount+1)
		}
	case "sub", "unsubscribe":
		if currentCount <= 0 {
			return nil
		}
		if currentCount-1 > 0 {
			s.tokenCounts[tokenAddr] = currentCount - 1
			log.Printf("[TokenService] Decreased monitoring count for token: %s (count: %d)", tokenAddr, currentCount-1)
		} else {
			delete(s.tokenCounts, tokenAddr)
			delete(s.tokenAddresses, tokenAddr)
			log.Printf("[TokenService] Stopped monitoring token: %s (count: 0)", tokenAddr)
		}
	default:
		if len(operation) == 0 {
			if currentCount > 0 {
				delete(s.tokenCounts, tokenAddr)
				delete(s.tokenAddresses, tokenAddr)
				log.Printf("[TokenService] Removed monitoring for token: %s (empty value)", tokenAddr)
			}
			return nil
		}
		s.tokenAddresses[tokenAddr] = operation
		s.tokenCounts[tokenAddr] = 1
		log.Printf("[TokenService] Added monitoring for token: %s -> %s (legacy mode)", tokenAddr, operation)
	}
	return nil
}

// IsTokenMonitored 检查某个代币地址是否需要被监控
func (s *TokenService) IsTokenMonitored(tokenAddr string) bool {
	s.mutex.RLock()
	count := s.tokenCounts[tokenAddr]
	s.mutex.RUnlock()
	return count > 0
}

// GetTargetAddress 获取监控代币对应的目标地址
func (s *TokenService) GetTargetAddress(tokenAddr string) (string, bool) {
	s.mutex.RLock()
	count := s.tokenCounts[tokenAddr]
	targetAddr, exists := s.tokenAddresses[tokenAddr]
	s.mutex.RUnlock()
	if count <= 0 || !exists {
		return "", false
	}
	return targetAddr, true
}

// GetAllMonitoredTokens 获取所有被监控的代币地址及其目标地址
func (s *TokenService) GetAllMonitoredTokens() map[string]string {
	result := make(map[string]string)
	s.mutex.RLock()
	for tokenAddr, count := range s.tokenCounts {
		if count > 0 {
			if targetAddr, exists := s.tokenAddresses[tokenAddr]; exists {
				result[tokenAddr] = targetAddr
			}
		}
	}
	s.mutex.RUnlock()
	return result
}

// GetTokenCount 获取特定代币地址的当前计数
func (s *TokenService) GetTokenCount(tokenAddr string) int {
	s.mutex.RLock()
	count := s.tokenCounts[tokenAddr]
	s.mutex.RUnlock()
	return count
}

// ForwardTransactionWithTarget 使用指定的目标地址将交易转发到Kafka
func (s *TokenService) ForwardTransactionWithTarget(targetAddr string, txData []byte) error {
	if err := s.producer.ProduceMessageToTopic("token_output", []byte(targetAddr), txData); err != nil {
		return fmt.Errorf("failed to forward transaction for token %s: %w", targetAddr, err)
	}
	return nil
}
