package service

import (
	"encoding/json"
	"fmt"
	"hash/fnv"
	"log"
	"sync"
	"time"

	"model-processor/model" // Import model package
	pb "model-processor/proto"
	txparser "model-processor/txparser"

	"github.com/gagliardetto/solana-go"
	"google.golang.org/protobuf/proto"
	// Assuming kafka client provides a Producer interface or similar
	// We need a way to send the produced message back.
	// Direct import of kafka package creates cycle, so pass producer func/interface
)

// KafkaProducer defines the interface needed by the service to produce messages.
// This avoids a direct dependency cycle between service and kafka.
type KafkaProducer interface {
	ProduceMessage(key, value []byte) error
}

// TransactionCallback 是交易处理完成后的回调函数
type TransactionCallback func(tx *model.MemeTx, mintAddr string, network string)

// TransactionService handles the business logic for processing transaction updates.
type TransactionService struct {
	kafkaProducer  KafkaProducer
	addressService *AddressService
	tokenService   *TokenService
	callbacks      []TransactionCallback
	database       model.TimeSeriesDB

	stats *StatsService

	// goroutine semaphore to limit concurrent goroutines
	goroutineSemaphore chan struct{}
}

// NewTransactionService creates a new service instance.
func NewTransactionService(kp KafkaProducer, stats *StatsService, db model.TimeSeriesDB) *TransactionService {
	if kp == nil {
		log.Println("Warning: KafkaProducer is nil in NewTransactionService")
	}
	if db == nil {
		log.Println("Warning: Database is nil in NewTransactionService")
	}
	return &TransactionService{
		kafkaProducer:      kp,
		stats:              stats,
		database:           db,
		goroutineSemaphore: make(chan struct{}, 500), // 增加到500个并发goroutines
	}
}

// SetAddressService sets the address service reference to check wallets
func (s *TransactionService) SetAddressService(addrService *AddressService) {
	s.addressService = addrService
}

// SetTokenService sets the token service reference to check monitored tokens
func (s *TransactionService) SetTokenService(tokenService *TokenService) {
	s.tokenService = tokenService
}

// safeGoroutine starts a goroutine with semaphore control to limit concurrent execution
func (s *TransactionService) safeGoroutine(fn func()) {
	s.goroutineSemaphore <- struct{}{} // Acquire semaphore
	go func() {
		defer func() {
			<-s.goroutineSemaphore // Release semaphore
		}()
		fn()
	}()
}

// RegisterCallback 注册一个新的交易回调
func (s *TransactionService) RegisterCallback(callback TransactionCallback) {
	s.callbacks = append(s.callbacks, callback)
}

// notifyCallbacks 通知所有注册的回调函数
func (s *TransactionService) notifyCallbacks(tx *model.MemeTx, mintAddr string, network string) {
	for _, callback := range s.callbacks {
		callback(tx, mintAddr, network)
	}
}

// FinalTx represents the final transaction format produced by the service.
// (Moved from kafka package)
type FinalTx struct {
	Ts               int64  `json:"ts"`
	TxHash           string `json:"tx_hash"`
	UserAddr         string `json:"user_addr"`
	TokenInAddr      string `json:"token_in_addr"`
	TokenOutAddr     string `json:"token_out_addr"`
	AmountIn         uint64 `json:"amount_in"`
	AmountOut        uint64 `json:"amount_out"`
	TxType           string `json:"tx_type"`
	BlockSlot        uint64 `json:"block_slot"`
	InstructionIndex int    `json:"instruction_index"`
	MintAddr         string `json:"mint_addr"`
	Network          string `json:"network"`
}

// transformSwapInfo transforms parser's SwapInfo into the FinalTx format.
// (Moved from kafka package)
func transformSwapInfo(swapInfo *txparser.SwapInfo, txHash string, blockSlot uint64, txIndex int) *FinalTx {
	blockTime := time.Now().Unix() // Default
	if !swapInfo.Timestamp.IsZero() {
		blockTime = swapInfo.Timestamp.Unix()
	}

	userAddr := ""
	if swapInfo.User != (solana.PublicKey{}) {
		userAddr = swapInfo.User.String()
	} else if len(swapInfo.Signers) > 0 {
		if len(swapInfo.Signers) > swapInfo.InstructionIndex {
			userAddr = swapInfo.Signers[swapInfo.InstructionIndex].String()
		} else {
			userAddr = swapInfo.Signers[0].String()
		}
	}
	txType := "SWAP"
	mintAddr := ""
	if swapInfo.TxType == "" {
		if swapInfo.TokenInMint.String() == WSOL_ADDRESS && swapInfo.TokenOutMint.String() == WSOL_ADDRESS {
			txType = "WSOL_INTERNAL"
			mintAddr = "WSOL_INTERNAL" // 或者其他合适的标记
		} else if swapInfo.TokenInMint.String() == WSOL_ADDRESS {
			txType = "BUY"
			mintAddr = swapInfo.TokenOutMint.String()
		} else if swapInfo.TokenOutMint.String() == WSOL_ADDRESS {
			txType = "SELL"
			mintAddr = swapInfo.TokenInMint.String()
		}
		// If neither in nor out is WSOL, and TxType is empty, it remains "SWAP"
		// and mintAddr remains "", which is handled by later logic if needed.
	} else {
		txType = swapInfo.TxType
		if txType == "BUY" {
			if swapInfo.TokenInMint.String() == WSOL_ADDRESS {
				mintAddr = swapInfo.TokenOutMint.String()
			} else {
				mintAddr = swapInfo.TokenInMint.String()
				txType = "SELL"
			}
		} else if txType == "SELL" { // Assuming "SELL" or other types where TokenInMint is relevant

			if swapInfo.TokenOutMint.String() == WSOL_ADDRESS {
				mintAddr = swapInfo.TokenInMint.String()
			} else {
				mintAddr = swapInfo.TokenOutMint.String()
				txType = "BUY"
			}
		}
	}
	return &FinalTx{
		Ts:               blockTime,
		TxHash:           txHash,
		UserAddr:         userAddr,
		TokenInAddr:      swapInfo.TokenInMint.String(),
		TokenOutAddr:     swapInfo.TokenOutMint.String(),
		AmountIn:         swapInfo.TokenInAmount,
		AmountOut:        swapInfo.TokenOutAmount,
		TxType:           txType,
		BlockSlot:        blockSlot,
		InstructionIndex: txIndex,
		MintAddr:         mintAddr,
		Network:          "solana",
	}
}

// mapFinalTxToMemeTx converts the service's FinalTx to the model's MemeTx for DB insertion.
func mapFinalTxToMemeTx(ft *FinalTx, txIndex uint64) model.MemeTx {
	// Ensure type conversions are correct (e.g., int to int32)
	return model.MemeTx{
		Ts:               time.Unix(ft.Ts, 0),
		BlockTime:        ft.Ts,
		TxHash:           ft.TxHash,
		UserAddr:         ft.UserAddr,
		TokenInAddr:      ft.TokenInAddr,
		TokenOutAddr:     ft.TokenOutAddr,
		AmountIn:         fmt.Sprintf("%d", ft.AmountIn),
		AmountOut:        fmt.Sprintf("%d", ft.AmountOut),
		TxType:           ft.TxType,
		BlockSlot:        int64(ft.BlockSlot),
		InstructionIndex: int32(ft.InstructionIndex),
		TxIndex:          int64(txIndex),

		// Network is a tag, passed separately to InsertMemeTx
	}
}

// 定义一些常量
const (
	// WSOL 代币地址
	WSOL_ADDRESS = "So11111111111111111111111111111111111111112"
)

// shortenAddress 将长地址缩短为更易读的格式 (前6位...后4位)
func shortenAddress(addr string) string {
	if len(addr) <= 12 {
		return addr
	}
	return addr[:6] + "..." + addr[len(addr)-4:]
}

func getThreadID(key string, bucketNum int) int {
	h := fnv.New32a()
	h.Write([]byte(key))
	return int(h.Sum32()) % bucketNum
}

// ProcessUpdate processes a raw message value, parses it as a transaction update,
// transforms it, saves to DB, and sends the result via the KafkaProducer.
func (s *TransactionService) ProcessUpdate(messageValue []byte, partitionID int32) error {
	// 记录分区统计
	s.stats.IncrementStat("partition_message", partitionID)

	var update pb.SubscribeUpdate
	if err := proto.Unmarshal(messageValue, &update); err != nil {
		s.stats.IncrementStat("parse_error")
		return nil
	}

	tx := update.GetTransaction()
	if tx == nil || tx.Transaction == nil || tx.Transaction.Transaction == nil {
		return nil
	}

	// txSigStr 用于统计和错误日志
	var txSigStr string
	if len(tx.Transaction.Transaction.Signatures) > 0 {
		txSigStr = solana.SignatureFromBytes(tx.Transaction.Transaction.Signatures[0]).String()
	} else {
		txSigStr = "<missing_signature>"
	}

	blockSlot := tx.Slot

	// 更新最大block slot统计
	s.stats.currentStats.UpdateMaxBlockSlot(blockSlot, txSigStr)

	parser, err := txparser.NewTransactionParser(tx)
	if err != nil {
		s.stats.IncrementStat("parse_error")
		return nil
	}

	parseTxStartTime := time.Now()
	transactionData, _, err := parser.ParseTransaction()
	s.stats.IncrementStat("total_parse_transaction_time", time.Since(parseTxStartTime))

	if err != nil {
		s.stats.IncrementStat("parse_error")
		return nil
	}

	if len(transactionData) == 0 {
		return nil
	}

	s.stats.IncrementStat("swap_tx")

	var wg sync.WaitGroup
	for instructionIndex, swapDatas := range transactionData {
		wg.Add(1)
		go func(idx int, sData []txparser.SwapData, currentBlockSlot uint64, currentTxSig string, txIndex uint64) {
			defer wg.Done()

			if len(sData) == 0 {
				return
			}
			s.stats.IncrementStat("instructions_attempted")

			processSwapDataStartTime := time.Now()
			swapInfos, err := parser.ProcessSwapData(sData)
			s.stats.IncrementStat("total_process_swap_data_time", time.Since(processSwapDataStartTime))

			if err != nil {
				s.stats.IncrementStat("process_error")
				return
			}
			for _, swapInfo := range swapInfos {

				tokenInAddress := swapInfo.TokenInMint.String()
				tokenOutAddress := swapInfo.TokenOutMint.String()
				if tokenInAddress != WSOL_ADDRESS && tokenOutAddress != WSOL_ADDRESS {
					return
				}

				s.stats.IncrementStat("instructions_processed")

				finalTx := transformSwapInfo(swapInfo, currentTxSig, currentBlockSlot, idx)
				finalTxCopy := *finalTx
				memeTx := mapFinalTxToMemeTx(&finalTxCopy, txIndex)
				memeTxCopy := memeTx
				// threadID := int(uintptr(unsafe.Pointer(&finalTxCopy)) % 10000)
				threadID := getThreadID(finalTxCopy.TxHash, 8) // 8个buffer

				// 1. 数据库写入 - Asynchronously
				s.safeGoroutine(func() {
					if s.database != nil {
						if errDb := s.database.InsertMemeTxBatch(memeTxCopy, finalTxCopy.Network, finalTxCopy.MintAddr, threadID); errDb != nil {
							s.stats.IncrementStat("db_error")
						} else {
							s.stats.IncrementStat("db_success")
						}
					} else {
						s.stats.IncrementStat("db_error")
					}
				})

				// 2. Kafka处理 - Asynchronously
				if s.kafkaProducer != nil {
					wg.Add(1)
					go func(ftxToProduce FinalTx) {
						defer wg.Done()
						jsonData, errJson := json.Marshal(ftxToProduce)
						if errJson != nil {
							s.stats.IncrementStat("kafka_produce_user_error")
							return
						}

						userAddr := ftxToProduce.UserAddr
						if s.addressService != nil {
							// 获取所有监控该地址的 groupId
							s.addressService.mutex.RLock()
							groupIds, exists := s.addressService.walletIndex[userAddr]
							s.addressService.mutex.RUnlock()
							if exists && len(groupIds) > 0 {
								for groupId := range groupIds {
									kafkaKey := groupId + "-" + userAddr
									if errKafka := s.kafkaProducer.ProduceMessage([]byte(kafkaKey), jsonData); errKafka != nil {
										s.stats.IncrementStat("kafka_produce_user_error")
									} else {
										s.stats.IncrementStat("kafka_produce_user_success")
									}
								}
							} else {
								s.stats.IncrementStat("address_filter")
							}
						}

						if s.tokenService != nil {
							tokensToCheck := []string{ftxToProduce.TokenInAddr, ftxToProduce.TokenOutAddr, ftxToProduce.MintAddr}
							for _, tokenAddr := range tokensToCheck {
								if tokenAddr == "" {
									continue
								}
								monitored := s.tokenService.IsTokenMonitored(tokenAddr)
								if monitored {
									log.Printf("[Kafka] ForwardTransactionWithTarget to token_output, tokenAddr: %s", tokenAddr)
									if errForward := s.tokenService.ForwardTransactionWithTarget(tokenAddr, jsonData); errForward != nil {
										s.stats.IncrementStat("kafka_forward_error")
									} else {
										s.stats.IncrementStat("token_forward")
									}
									break
								}
							}
						}
					}(finalTxCopy)
				}

			}
		}(instructionIndex, swapDatas, blockSlot, txSigStr, parser.GetTxIndex())

	}

	wg.Wait()
	return nil
}
