package txparser

import (
	"bytes"
	"model-processor/proto"

	"github.com/gagliardetto/solana-go"
)

// isTransfer checks if the instruction is a token transfer (Raydium, Orca)
func (p *Parser) isTransfer(instr *proto.InnerInstruction) bool {
	progID := p.allAccountKeys[instr.ProgramIdIndex]

	if !progID.Equals(solana.TokenProgramID) {
		return false
	}

	if len(instr.Accounts) < 3 || len(instr.Data) < 9 {
		return false
	}

	if instr.Data[0] != 3 {
		return false
	}

	for i := 0; i < 3; i++ {
		if int(instr.Accounts[i]) >= len(p.allAccountKeys) {
			return false
		}
	}

	return true
}

// isTransferCheck checks if the instruction is a token transfer check (Meteora)
func (p *Parser) isTransferCheck(instr *proto.InnerInstruction) bool {
	progID := p.allAccountKeys[instr.ProgramIdIndex]

	if !progID.Equals(solana.TokenProgramID) && !progID.Equals(solana.Token2022ProgramID) {
		return false
	}

	if len(instr.Accounts) < 4 || len(instr.Data) < 9 {
		return false
	}

	if instr.Data[0] != 12 {
		return false
	}

	for i := 0; i < 4; i++ {
		if int(instr.Accounts[i]) >= len(p.allAccountKeys) {
			return false
		}
	}

	return true
}

func (p *Parser) isPumpFunTradeEventInstruction(inst *proto.InnerInstruction) bool {
	if !p.allAccountKeys[inst.ProgramIdIndex].Equals(PUMP_FUN_PROGRAM_ID) || len(inst.Data) < 16 {
		return false
	}
	decodedBytes := inst.Data
	return bytes.Equal(decodedBytes[:16], PumpfunTradeEventDiscriminator[:])
}

func (p *Parser) isPumpFunCreateEventInstruction(inst *proto.InnerInstruction) bool {
	if !p.allAccountKeys[inst.ProgramIdIndex].Equals(PUMP_FUN_PROGRAM_ID) || len(inst.Data) < 16 {
		return false
	}
	decodedBytes := inst.Data
	return bytes.Equal(decodedBytes[:16], PumpfunCreateEventDiscriminator[:])
}

func (p *Parser) isJupiterRouteEventInstruction(inst *proto.InnerInstruction) bool {
	if !p.allAccountKeys[inst.ProgramIdIndex].Equals(JUPITER_PROGRAM_ID) || len(inst.Data) < 16 {
		return false
	}
	decodedBytes := inst.Data
	return bytes.Equal(decodedBytes[:16], JupiterRouteEventDiscriminator[:])
}

func (p *Parser) isPumpSwapBuyEventInstruction(inst *proto.InnerInstruction) bool {
	if !p.allAccountKeys[inst.ProgramIdIndex].Equals(PUMP_SWAP_PROGRAM_ID) || len(inst.Data) < 16 {
		return false
	}
	decodedBytes := inst.Data
	return bytes.Equal(decodedBytes[:16], PumpSwapBuyEventDiscriminator[:])
}
func (p *Parser) isPumpSwapSellEventInstruction(inst *proto.InnerInstruction) bool {
	if !p.allAccountKeys[inst.ProgramIdIndex].Equals(PUMP_SWAP_PROGRAM_ID) || len(inst.Data) < 16 {
		return false
	}
	decodedBytes := inst.Data
	return bytes.Equal(decodedBytes[:16], PumpSwapSellEventDiscriminator[:])
}

func (p *Parser) isPumpSwapCreateEventInstruction(inst *proto.InnerInstruction) bool {
	if !p.allAccountKeys[inst.ProgramIdIndex].Equals(PUMP_SWAP_PROGRAM_ID) || len(inst.Data) < 16 {
		return false
	}
	decodedBytes := inst.Data
	return bytes.Equal(decodedBytes[:16], PumpSwapCreateEventDiscriminator[:])
}
