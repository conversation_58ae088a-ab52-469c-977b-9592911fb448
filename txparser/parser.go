package txparser

import (
	"fmt"
	"model-processor/proto"
	"strconv"
	"time"

	"github.com/gagliardetto/solana-go"
	"github.com/sirupsen/logrus"
)

const (
	PROTOCOL_RAYDIUM  = "raydium"
	PROTOCOL_ORCA     = "orca"
	PROTOCOL_METEORA  = "meteora"
	PROTOCOL_PUMPFUN  = "pumpfun"
	PROTOCOL_PUMPSWAP = "pumpswap"
)

type TokenTransfer struct {
	mint     string
	amount   uint64
	decimals uint32
}

type Parser struct {
	txMeta          *proto.TransactionStatusMeta
	txIndex         uint64
	txInfo          *proto.Transaction
	allAccountKeys  solana.PublicKeySlice
	splTokenInfoMap map[string]TokenInfo
	splDecimalsMap  map[string]uint32
	Log             *logrus.Logger
}

func NewTransactionParser(tx *proto.SubscribeUpdateTransaction) (*Parser, error) {
	txInfo := tx.Transaction.GetTransaction()
	index := tx.Transaction.GetIndex()

	return NewTransactionParserFromTransaction(txInfo, tx.Transaction.Meta, index)
}

func NewTransactionParserFromTransaction(tx *proto.Transaction, txMeta *proto.TransactionStatusMeta, txIndex uint64) (*Parser, error) {
	var allAccountKeys solana.PublicKeySlice
	//allAccountKeys := append(tx.Message.AccountKeys,
	//	txMeta.LoadedWritableAddresses...)
	//allAccountKeys = append(allAccountKeys, txMeta.LoadedReadonlyAddresses...)
	for _, keyBytes := range tx.Message.AccountKeys {
		allAccountKeys = append(allAccountKeys, solana.PublicKeyFromBytes(keyBytes))
	}
	for _, keyBytes := range txMeta.LoadedWritableAddresses {
		allAccountKeys = append(allAccountKeys, solana.PublicKeyFromBytes(keyBytes))
	}
	for _, keyBytes := range txMeta.LoadedReadonlyAddresses {
		allAccountKeys = append(allAccountKeys, solana.PublicKeyFromBytes(keyBytes))
	}
	log := logrus.New()
	log.SetFormatter(&logrus.TextFormatter{
		TimestampFormat: "2006-01-02 15:04:05",
		FullTimestamp:   true,
	})

	parser := &Parser{
		txMeta:         txMeta,
		txInfo:         tx,
		txIndex:        txIndex,
		allAccountKeys: allAccountKeys,
		Log:            log,
	}

	// testing: Log if PumpSwap is detected
	//for _, key := range allAccountKeys {
	//	if key.Equals(PUMP_SWAP_PROGRAM_ID) {
	//		parser.Log.Infof("Detected PumpSwap transaction with program ID: %s", PUMP_SWAP_PROGRAM_ID)
	//		// break
	//	}
	//}

	if err := parser.extractSPLTokenInfo(); err != nil {
		return nil, fmt.Errorf("failed to extract SPL Token Addresses: %w", err)
	}

	if err := parser.extractSPLDecimals(); err != nil {
		return nil, fmt.Errorf("failed to extract SPL decimals: %w", err)
	}

	return parser, nil
}

type SwapData struct {
	Type             SwapType
	TxType           string
	InstructionIndex int
	Data             interface{}
}

type CreateData struct {
	Type             CreateType
	InstructionIndex int
	Data             interface{}
}

func (p *Parser) ParseTransaction() (map[int][]SwapData, map[int][]CreateData, error) {
	parsedSwaps := make(map[int][]SwapData)
	parsedCreates := make(map[int][]CreateData)

	// 处理外部指令，初始化交换数据
	for i, outerInstruction := range p.txInfo.Message.Instructions {
		progID := p.allAccountKeys[outerInstruction.ProgramIdIndex]
		switch {
		case progID.Equals(JUPITER_PROGRAM_ID):
			if _, exists := parsedSwaps[i]; !exists {
				parsedSwaps[i] = []SwapData{}
			}
			parsedSwaps[i] = append(parsedSwaps[i], p.processJupiterSwaps(i)...)
		case progID.Equals(MOONSHOT_PROGRAM_ID):
			if _, exists := parsedSwaps[i]; !exists {
				parsedSwaps[i] = []SwapData{}
			}
			parsedSwaps[i] = append(parsedSwaps[i], p.processMoonshotSwaps()...)
		case p.isRouterProgram(progID):
			if _, exists := parsedSwaps[i]; !exists {
				parsedSwaps[i] = []SwapData{}
			}
			parsedSwaps[i] = append(parsedSwaps[i], p.processRouterSwaps(i)...)
		case progID.Equals(OKX_DEX_ROUTER_PROGRAM_ID):
			if _, exists := parsedSwaps[i]; !exists {
				parsedSwaps[i] = []SwapData{}
			}
			parsedSwaps[i] = append(parsedSwaps[i], p.processOKXSwaps(i)...)
		}
	}

	// 如果需要跳过，则直接返回已解析的交换数据和创建数据
	if len(parsedSwaps) > 0 {
		return parsedSwaps, parsedCreates, nil
	}
	// 处理其他交换程序
	for i, outerInstruction := range p.txInfo.Message.Instructions {
		progID := p.allAccountKeys[outerInstruction.ProgramIdIndex]
		fmt.Println()
		switch {
		case p.isRaydiumProgram(progID):
			if _, exists := parsedSwaps[i]; !exists {
				parsedSwaps[i] = []SwapData{}
			}
			parsedSwaps[i] = append(parsedSwaps[i], p.processRaydSwaps(i)...)
		case progID.Equals(ORCA_PROGRAM_ID):
			if _, exists := parsedSwaps[i]; !exists {
				parsedSwaps[i] = []SwapData{}
			}
			parsedSwaps[i] = append(parsedSwaps[i], p.processOrcaSwaps(i)...)
		case p.isMeteoraProgram(progID):
			if _, exists := parsedSwaps[i]; !exists {
				parsedSwaps[i] = []SwapData{}
			}
			parsedSwaps[i] = append(parsedSwaps[i], p.processMeteoraSwaps(i)...)
		case p.isPumpfunProgram(progID):
			if _, exists := parsedSwaps[i]; !exists {
				parsedSwaps[i] = []SwapData{}
			}
			swaps, creates := p.processPumpfunSwaps(i)
			parsedSwaps[i] = append(parsedSwaps[i], swaps...)
			parsedCreates[i] = append(parsedCreates[i], creates...)
		case p.isPumpSwapProgram(progID):
			if _, exists := parsedSwaps[i]; !exists {
				parsedSwaps[i] = []SwapData{}
			}
			parsedSwaps[i] = append(parsedSwaps[i], p.processPumpSwapSwaps(i)...)
		}
	}

	return parsedSwaps, parsedCreates, nil
}

type SwapInfo struct {
	Signers          []solana.PublicKey
	Signatures       []solana.Signature
	TxType           string
	AMMs             []string
	Timestamp        time.Time
	TokenInMint      solana.PublicKey
	TokenInAmount    uint64
	TokenInDecimals  uint32
	InstructionIndex int
	TokenOutMint     solana.PublicKey
	TokenOutAmount   uint64
	TokenOutDecimals uint32
	User             solana.PublicKey
}

func (p *Parser) ProcessSwapData(swapDatas []SwapData) ([]*SwapInfo, error) {
	swapInfos := make([]*SwapInfo, 0, len(swapDatas))
	if len(swapDatas) == 0 {
		return nil, fmt.Errorf("no swap data provided")
	}

	var sigs []solana.Signature
	for _, sigBytes := range p.txInfo.Signatures {
		sig := solana.SignatureFromBytes(sigBytes)
		sigs = append(sigs, sig)
	}
	swapInfo := &SwapInfo{
		Signatures: sigs,
	}

	if p.containsDCAProgram() {
		swapInfo.Signers = []solana.PublicKey{p.allAccountKeys[2]}
	} else {
		swapInfo.Signers = []solana.PublicKey{p.allAccountKeys[0]}
	}

	jupiterSwaps := make([]SwapData, 0)
	pumpfunSwaps := make([]SwapData, 0)
	pumpswapSwaps := make([]SwapData, 0) // newly added
	pumpswapCreateSwaps := make([]SwapData, 0)
	otherSwaps := make([]SwapData, 0)

	for _, swapData := range swapDatas {
		switch swapData.Type {
		case JUPITER:
			jupiterSwaps = append(jupiterSwaps, swapData)
		case PUMP_FUN:
			pumpfunSwaps = append(pumpfunSwaps, swapData)
		case PUMP_SWAP:
			pumpswapSwaps = append(pumpswapSwaps, swapData)
		case PUMP_SWAP_CREATE:
			pumpswapCreateSwaps = append(pumpswapCreateSwaps, swapData)
		default:
			otherSwaps = append(otherSwaps, swapData)
		}
	}

	if len(jupiterSwaps) > 0 {
		jupiterInfo, err := parseJupiterEvents(jupiterSwaps)
		if err != nil {
			return nil, fmt.Errorf("failed to parse Jupiter events: %w", err)
		}

		swapInfo.TokenInMint = jupiterInfo.TokenInMint
		swapInfo.TokenInAmount = jupiterInfo.TokenInAmount
		swapInfo.TokenInDecimals = jupiterInfo.TokenInDecimals
		swapInfo.TokenOutMint = jupiterInfo.TokenOutMint
		swapInfo.TokenOutAmount = jupiterInfo.TokenOutAmount
		swapInfo.TokenOutDecimals = jupiterInfo.TokenOutDecimals
		swapInfo.AMMs = jupiterInfo.AMMs
		swapInfos = append(swapInfos, swapInfo)
	}

	if len(pumpfunSwaps) > 0 {
		for _, swapData := range pumpfunSwaps {
			event := swapData.Data.(*PumpfunTradeEvent)
			if event.IsBuy {
				swapInfo.TokenInMint = NATIVE_SOL_MINT_PROGRAM_ID
				swapInfo.TokenInAmount = event.SolAmount
				swapInfo.TokenInDecimals = 9
				swapInfo.TokenOutMint = event.Mint
				swapInfo.TokenOutAmount = event.TokenAmount
				swapInfo.TokenOutDecimals = p.splDecimalsMap[event.Mint.String()]
				swapInfo.TxType = swapData.TxType
			} else {
				swapInfo.TokenInMint = event.Mint
				swapInfo.TokenInAmount = event.TokenAmount
				swapInfo.TokenInDecimals = p.splDecimalsMap[event.Mint.String()]
				swapInfo.TokenOutMint = NATIVE_SOL_MINT_PROGRAM_ID
				swapInfo.TokenOutAmount = event.SolAmount
				swapInfo.TokenOutDecimals = 9
				swapInfo.TxType = swapData.TxType

			}
			swapInfo.AMMs = append(swapInfo.AMMs, string(pumpfunSwaps[0].Type))
			swapInfo.Timestamp = time.Unix(event.Timestamp, 0)
			swapInfo.User = event.User
			swapInfos = append(swapInfos, swapInfo)
		}
	}

	//newly added
	if len(pumpswapSwaps) > 0 {
		// 先检查第一个数据，避免多余循环makde
		for _, swapData := range pumpswapSwaps {
			if swapData.TxType == "BUY" {
				event, ok := swapData.Data.(*PumpSwapBuyEvent)
				if !ok {
					continue
				}
				swapInfo.Timestamp = time.Unix(event.Timestamp, 0)
				swapInfo.TokenInAmount = event.MaxQuoteAmountIn
				swapInfo.Signers = append(swapInfo.Signers, event.User)
				swapInfo.TokenInMint = event.UserBaseTokenAccount
				swapInfo.TokenOutAmount = event.BaseAmountOut
				swapInfo.TokenOutMint = event.UserQuoteTokenAccount
				swapInfo.User = event.User
				// 为Sell事件设置类型
				swapInfo.TxType = swapData.TxType
				swapInfos = append(swapInfos, swapInfo)
			} else {
				event, ok := swapData.Data.(*PumpSwapSellEvent)
				if !ok {
					continue
				}
				swapInfo.Timestamp = time.Unix(event.Timestamp, 0)
				swapInfo.TokenInAmount = event.BaseAmountIn
				swapInfo.Signers = append(swapInfo.Signers, event.User)
				swapInfo.TokenInMint = event.UserBaseTokenAccount
				swapInfo.TokenOutAmount = event.UserQuoteAmountOut
				swapInfo.TokenOutMint = event.UserQuoteTokenAccount
				swapInfo.User = event.User
				// 为Sell事件设置类型
				swapInfo.TxType = swapData.TxType
				swapInfos = append(swapInfos, swapInfo)
			}

		}
	}

	if len(pumpswapCreateSwaps) > 0 {
		event, ok := pumpswapCreateSwaps[0].Data.(*PumpSwapCreateEvent)
		if !ok {
			return nil, fmt.Errorf("expected *PumpSwapCreateEvent but got %T", pumpswapCreateSwaps[0].Data)
		}

		swapInfo.Timestamp = time.Unix(event.Timestamp, 0)
		swapInfo.TokenInAmount = event.PoolBaseAmount
		swapInfo.TokenInMint = event.BaseMint
		swapInfo.Signers = append(swapInfo.Signers, event.Creator)
		swapInfo.TokenOutAmount = event.PoolQuoteAmount
		swapInfo.TokenOutMint = event.QuoteMint
		swapInfo.TxType = "ADD"
		swapInfos = append(swapInfos, swapInfo)
	}
	if len(otherSwaps) > 0 {
		var uniqueTokens []TokenTransfer
		seenTokens := make(map[string]bool)

		for _, swapData := range otherSwaps {
			transfer := getTransferFromSwapData(swapData)
			if transfer != nil && !seenTokens[transfer.mint] {
				uniqueTokens = append(uniqueTokens, *transfer)
				seenTokens[transfer.mint] = true
			}
		}

		if len(uniqueTokens) >= 2 {
			inputTransfer := uniqueTokens[0]
			outputTransfer := uniqueTokens[len(uniqueTokens)-1]

			seenInputs := make(map[string]bool)
			seenOutputs := make(map[string]bool)
			var totalInputAmount uint64 = 0
			var totalOutputAmount uint64 = 0

			for _, swapData := range otherSwaps {
				transfer := getTransferFromSwapData(swapData)
				if transfer == nil {
					continue
				}

				amountStr := fmt.Sprintf("%d-%s", transfer.amount, transfer.mint)
				if transfer.mint == inputTransfer.mint && !seenInputs[amountStr] {
					totalInputAmount += transfer.amount
					seenInputs[amountStr] = true
				}
				if transfer.mint == outputTransfer.mint && !seenOutputs[amountStr] {
					totalOutputAmount += transfer.amount
					seenOutputs[amountStr] = true
				}
			}

			swapInfo.TokenInMint = solana.MustPublicKeyFromBase58(inputTransfer.mint)
			swapInfo.TokenInAmount = totalInputAmount
			swapInfo.TokenInDecimals = inputTransfer.decimals
			swapInfo.TokenOutMint = solana.MustPublicKeyFromBase58(outputTransfer.mint)
			swapInfo.TokenOutAmount = totalOutputAmount
			swapInfo.TokenOutDecimals = outputTransfer.decimals

			seenAMMs := make(map[string]bool)
			for _, swapData := range otherSwaps {
				if !seenAMMs[string(swapData.Type)] {
					swapInfo.AMMs = append(swapInfo.AMMs, string(swapData.Type))
					seenAMMs[string(swapData.Type)] = true
				}
			}

			swapInfo.Timestamp = time.Now()
			swapInfos = append(swapInfos, swapInfo)
		}
	}

	return swapInfos, nil
}

func (p *Parser) ProcessCreateData(createDatas []CreateData) ([]*PumpfunCreateEvent, error) {
	if len(createDatas) == 0 {
		return nil, fmt.Errorf("no swap data provided")
	}

	var sigs []solana.Signature
	for _, sigBytes := range p.txInfo.Signatures {
		sig := solana.SignatureFromBytes(sigBytes)
		sigs = append(sigs, sig)
	}
	swapInfo := &SwapInfo{
		Signatures: sigs,
	}

	if p.containsDCAProgram() {
		swapInfo.Signers = []solana.PublicKey{p.allAccountKeys[2]}
	} else {
		swapInfo.Signers = []solana.PublicKey{p.allAccountKeys[0]}
	}

	pumpfunCreate := make([]CreateData, 0)
	for _, createData := range createDatas {
		switch createData.Type {
		case PUMP_FUN_CREATE:
			pumpfunCreate = append(pumpfunCreate, createData)
		}
	}
	if len(pumpfunCreate) > 0 {
		events := make([]*PumpfunCreateEvent, 0, len(pumpfunCreate))
		for _, createData := range pumpfunCreate {
			event := createData.Data.(*PumpfunCreateEvent)
			events = append(events, event)
		}
		return events, nil
	}
	return nil, fmt.Errorf("no valid swaps found")
}

func (p *Parser) GetTxIndex() uint64 {
	return p.txIndex
}
func getTransferFromSwapData(swapData SwapData) *TokenTransfer {
	switch data := swapData.Data.(type) {
	case *TransferData:
		return &TokenTransfer{
			mint:     data.Mint,
			amount:   data.Info.Amount,
			decimals: data.Decimals,
		}
	case *TransferCheck:
		amt, err := strconv.ParseUint(data.Info.TokenAmount.Amount, 10, 64)
		if err != nil {
			return nil
		}
		return &TokenTransfer{
			mint:     data.Info.Mint,
			amount:   amt,
			decimals: data.Info.TokenAmount.Decimals,
		}
	}
	return nil
}

func (p *Parser) processRouterSwaps(instructionIndex int) []SwapData {
	var swaps []SwapData

	innerInstructions := p.getInnerInstructions(instructionIndex)
	if len(innerInstructions) == 0 {
		return swaps
	}

	processedProtocols := make(map[string]bool)

	for _, inner := range innerInstructions {
		progID := p.allAccountKeys[inner.ProgramIdIndex]

		switch {
		case (progID.Equals(RAYDIUM_V4_PROGRAM_ID) ||
			progID.Equals(RAYDIUM_CPMM_PROGRAM_ID) ||
			progID.Equals(RAYDIUM_AMM_PROGRAM_ID) ||
			progID.Equals(RAYDIUM_CONCENTRATED_LIQUIDITY_PROGRAM_ID)) && !processedProtocols[PROTOCOL_RAYDIUM]:
			processedProtocols[PROTOCOL_RAYDIUM] = true
			if raydSwaps := p.processRaydSwaps(instructionIndex); len(raydSwaps) > 0 {
				swaps = append(swaps, raydSwaps...)
			}

		case progID.Equals(ORCA_PROGRAM_ID) && !processedProtocols[PROTOCOL_ORCA]:
			processedProtocols[PROTOCOL_ORCA] = true
			if orcaSwaps := p.processOrcaSwaps(instructionIndex); len(orcaSwaps) > 0 {
				swaps = append(swaps, orcaSwaps...)
			}

		case (progID.Equals(METEORA_PROGRAM_ID) ||
			progID.Equals(METEORA_POOLS_PROGRAM_ID)) && !processedProtocols[PROTOCOL_METEORA]:
			processedProtocols[PROTOCOL_METEORA] = true
			if meteoraSwaps := p.processMeteoraSwaps(instructionIndex); len(meteoraSwaps) > 0 {
				swaps = append(swaps, meteoraSwaps...)
			}

		case (progID.Equals(PUMP_FUN_PROGRAM_ID) ||
			progID.Equals(solana.MustPublicKeyFromBase58("BSfD6SHZigAfDWSjzD5Q41jw8LmKwtmjskPH9XW1mrRW"))) && !processedProtocols[PROTOCOL_PUMPFUN]:
			processedProtocols[PROTOCOL_PUMPFUN] = true
			if pumpfunSwaps, _ := p.processPumpfunSwaps(instructionIndex); len(pumpfunSwaps) > 0 {
				swaps = append(swaps, pumpfunSwaps...)
			}
		}
	}

	return swaps
}

func (p *Parser) getInnerInstructions(index int) []*proto.InnerInstruction {
	if p.txMeta == nil || p.txMeta.InnerInstructions == nil {
		return nil
	}

	for _, inner := range p.txMeta.InnerInstructions {
		if inner.Index == uint32(index) {
			return inner.Instructions
		}
	}

	return nil
}
